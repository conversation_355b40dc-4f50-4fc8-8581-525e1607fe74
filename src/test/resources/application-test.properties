# Test Configuration
spring.application.name=generated-rest-api-test

# H2 Test Database Configuration
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.h2.console.enabled=false

# JPA Configuration for Tests
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Security Configuration for Tests
app.jwt.secret=testSecretKey123456789012345678901234567890
app.jwt.expiration=86400000
app.jwt.refresh-expiration=604800000

# Disable Actuator endpoints in tests
management.endpoints.enabled-by-default=false

# Logging Configuration for Tests
logging.level.com.example.restapi=WARN
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=WARN
logging.level.org.hibernate.SQL=WARN

# Disable OpenAPI in tests
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false

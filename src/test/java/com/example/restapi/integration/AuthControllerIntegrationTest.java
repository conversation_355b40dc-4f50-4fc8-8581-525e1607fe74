package com.example.restapi.integration;

import com.example.restapi.dto.CreateUserRequest;
import com.example.restapi.dto.LoginRequest;
import com.example.restapi.entity.CorporateUser;
import com.example.restapi.repository.UserRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class AuthControllerIntegrationTest {

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                .apply(springSecurity())
                .build();
        
        // Clean up database
        userRepository.deleteAll();
    }

    @Test
    void testSuccessfulRegistration() throws Exception {
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("testuser");
        request.setEmail("<EMAIL>");
        request.setPassword("TestPass123!");
        request.setFirstName("Test");
        request.setLastName("User");
        request.setUserType("CORPORATE");
        request.setCompanyName("Test Company");

        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.username").value("testuser"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.userType").value("CORPORATE"));
    }

    @Test
    void testRegistrationWithDuplicateUsername() throws Exception {
        // Create existing user
        CorporateUser existingUser = new CorporateUser("testuser", "<EMAIL>", 
                passwordEncoder.encode("password"), "Existing", "User", "Existing Company");
        userRepository.save(existingUser);

        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("testuser");
        request.setEmail("<EMAIL>");
        request.setPassword("TestPass123!");
        request.setFirstName("New");
        request.setLastName("User");
        request.setUserType("CORPORATE");
        request.setCompanyName("New Company");

        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("Username is already taken!"));
    }

    @Test
    void testSuccessfulLogin() throws Exception {
        // Create test user
        CorporateUser user = new CorporateUser("testuser", "<EMAIL>", 
                passwordEncoder.encode("TestPass123!"), "Test", "User", "Test Company");
        userRepository.save(user);

        LoginRequest loginRequest = new LoginRequest("testuser", "TestPass123!");

        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.token").exists())
                .andExpect(jsonPath("$.refreshToken").exists())
                .andExpect(jsonPath("$.username").value("testuser"))
                .andExpect(jsonPath("$.userType").value("CORPORATE"));
    }

    @Test
    void testLoginWithInvalidCredentials() throws Exception {
        LoginRequest loginRequest = new LoginRequest("nonexistent", "wrongpassword");

        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void testRegistrationWithInvalidPassword() throws Exception {
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("testuser");
        request.setEmail("<EMAIL>");
        request.setPassword("weak"); // Too weak password
        request.setFirstName("Test");
        request.setLastName("User");
        request.setUserType("CORPORATE");
        request.setCompanyName("Test Company");

        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.validationErrors").exists());
    }

    @Test
    void testRegistrationWithInvalidEmail() throws Exception {
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("testuser");
        request.setEmail("invalid-email"); // Invalid email format
        request.setPassword("TestPass123!");
        request.setFirstName("Test");
        request.setLastName("User");
        request.setUserType("CORPORATE");
        request.setCompanyName("Test Company");

        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.validationErrors.email").exists());
    }

    @Test
    void testRegistrationWithSqlInjectionAttempt() throws Exception {
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("'; DROP TABLE users; --");
        request.setEmail("<EMAIL>");
        request.setPassword("TestPass123!");
        request.setFirstName("Test");
        request.setLastName("User");
        request.setUserType("CORPORATE");
        request.setCompanyName("Test Company");

        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.validationErrors.username").exists());
    }

    @Test
    void testLogout() throws Exception {
        mockMvc.perform(post("/api/auth/logout")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Logout successful"));
    }
}

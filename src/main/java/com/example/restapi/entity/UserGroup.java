package com.example.restapi.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Entity
@Table(name = "user_groups", indexes = {
    @Index(name = "idx_usergroup_name", columnList = "name"),
    @Index(name = "idx_usergroup_status", columnList = "status")
})
@EntityListeners(AuditingEntityListener.class)
public class UserGroup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Group name is required")
    @Size(min = 2, max = 100, message = "Group name must be between 2 and 100 characters")
    @Column(unique = true, nullable = false, length = 100)
    private String name;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    @Column(length = 500)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private GroupStatus status = GroupStatus.ACTIVE;

    @Size(max = 50, message = "Group type must not exceed 50 characters")
    @Column(name = "group_type", length = 50)
    private String groupType;

    @Column(name = "max_members")
    private Integer maxMembers;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
        name = "user_group_members",
        joinColumns = @JoinColumn(name = "user_group_id"),
        inverseJoinColumns = @JoinColumn(name = "user_id"),
        indexes = {
            @Index(name = "idx_user_group_members_group", columnList = "user_group_id"),
            @Index(name = "idx_user_group_members_user", columnList = "user_id")
        }
    )
    private Set<User> users = new HashSet<>();

    @ManyToMany(mappedBy = "userGroups", fetch = FetchType.LAZY)
    private Set<Company> companies = new HashSet<>();

    // Constructors
    public UserGroup() {}

    public UserGroup(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public UserGroup(String name, String description, String groupType) {
        this.name = name;
        this.description = description;
        this.groupType = groupType;
    }

    // Utility methods for managing users
    public void addUser(User user) {
        if (maxMembers != null && users.size() >= maxMembers) {
            throw new IllegalStateException("Cannot add user: group has reached maximum capacity of " + maxMembers);
        }
        users.add(user);
        user.getUserGroups().add(this);
    }

    public void removeUser(User user) {
        users.remove(user);
        user.getUserGroups().remove(this);
    }

    public boolean hasUser(User user) {
        return users.contains(user);
    }

    public int getUserCount() {
        return users.size();
    }

    public boolean isActive() {
        return status == GroupStatus.ACTIVE;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public GroupStatus getStatus() {
        return status;
    }

    public void setStatus(GroupStatus status) {
        this.status = status;
    }

    public String getGroupType() {
        return groupType;
    }

    public void setGroupType(String groupType) {
        this.groupType = groupType;
    }

    public Integer getMaxMembers() {
        return maxMembers;
    }

    public void setMaxMembers(Integer maxMembers) {
        this.maxMembers = maxMembers;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Set<User> getUsers() {
        return users;
    }

    public void setUsers(Set<User> users) {
        this.users = users;
    }

    public Set<Company> getCompanies() {
        return companies;
    }

    public void setCompanies(Set<Company> companies) {
        this.companies = companies;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof UserGroup)) return false;
        UserGroup userGroup = (UserGroup) o;
        return Objects.equals(id, userGroup.id) && Objects.equals(name, userGroup.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name);
    }

    @Override
    public String toString() {
        return "UserGroup{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", status=" + status +
                ", groupType='" + groupType + '\'' +
                ", maxMembers=" + maxMembers +
                ", userCount=" + getUserCount() +
                '}';
    }

    // Enum for group status
    public enum GroupStatus {
        ACTIVE,
        INACTIVE,
        SUSPENDED,
        ARCHIVED
    }
}

package com.example.restapi.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

@Entity
@DiscriminatorValue("CORPORATE")
public class CorporateUser extends User {

    @NotBlank(message = "Company name is required")
    @Size(max = 100, message = "Company name must not exceed 100 characters")
    @Column(name = "company_name", length = 100)
    private String companyName;

    @Size(max = 50, message = "Department must not exceed 50 characters")
    @Column(name = "department", length = 50)
    private String department;

    @Size(max = 50, message = "Job title must not exceed 50 characters")
    @Column(name = "job_title", length = 50)
    private String jobTitle;

    @Size(max = 20, message = "Employee ID must not exceed 20 characters")
    @Column(name = "employee_id", length = 20)
    private String employeeId;

    // Constructors
    public CorporateUser() {
        super();
    }

    public CorporateUser(String username, String email, String password, String firstName, String lastName, String companyName) {
        super(username, email, password, firstName, lastName);
        this.companyName = companyName;
    }

    @Override
    public String getUserType() {
        return "CORPORATE";
    }

    // Getters and Setters
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    @Override
    public String toString() {
        return "CorporateUser{" +
                "id=" + getId() +
                ", username='" + getUsername() + '\'' +
                ", email='" + getEmail() + '\'' +
                ", firstName='" + getFirstName() + '\'' +
                ", lastName='" + getLastName() + '\'' +
                ", companyName='" + companyName + '\'' +
                ", department='" + department + '\'' +
                ", jobTitle='" + jobTitle + '\'' +
                ", employeeId='" + employeeId + '\'' +
                ", enabled=" + getEnabled() +
                '}';
    }
}

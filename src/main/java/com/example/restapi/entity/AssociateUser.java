package com.example.restapi.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Pattern;

@Entity
@DiscriminatorValue("ASSOCIATE")
public class AssociateUser extends User {

    @NotBlank(message = "Associate ID is required")
    @Size(max = 20, message = "Associate ID must not exceed 20 characters")
    @Pattern(regexp = "^[A-Z0-9]+$", message = "Associate ID must contain only uppercase letters and numbers")
    @Column(name = "associate_id", unique = true, nullable = false, length = 20)
    private String associateId;

    @Size(max = 100, message = "Organization name must not exceed 100 characters")
    @Column(name = "organization_name", length = 100)
    private String organizationName;

    @Size(max = 50, message = "Certification must not exceed 50 characters")
    @Column(name = "certification", length = 50)
    private String certification;

    @Size(max = 50, message = "Specialization must not exceed 50 characters")
    @Column(name = "specialization", length = 50)
    private String specialization;

    @Column(name = "years_of_experience")
    private Integer yearsOfExperience;

    // Constructors
    public AssociateUser() {
        super();
    }

    public AssociateUser(String username, String email, String password, String firstName, String lastName, String associateId) {
        super(username, email, password, firstName, lastName);
        this.associateId = associateId;
    }

    @Override
    public String getUserType() {
        return "ASSOCIATE";
    }

    // Getters and Setters
    public String getAssociateId() {
        return associateId;
    }

    public void setAssociateId(String associateId) {
        this.associateId = associateId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getCertification() {
        return certification;
    }

    public void setCertification(String certification) {
        this.certification = certification;
    }

    public String getSpecialization() {
        return specialization;
    }

    public void setSpecialization(String specialization) {
        this.specialization = specialization;
    }

    public Integer getYearsOfExperience() {
        return yearsOfExperience;
    }

    public void setYearsOfExperience(Integer yearsOfExperience) {
        this.yearsOfExperience = yearsOfExperience;
    }

    @Override
    public String toString() {
        return "AssociateUser{" +
                "id=" + getId() +
                ", username='" + getUsername() + '\'' +
                ", email='" + getEmail() + '\'' +
                ", firstName='" + getFirstName() + '\'' +
                ", lastName='" + getLastName() + '\'' +
                ", associateId='" + associateId + '\'' +
                ", organizationName='" + organizationName + '\'' +
                ", certification='" + certification + '\'' +
                ", specialization='" + specialization + '\'' +
                ", yearsOfExperience=" + yearsOfExperience +
                ", enabled=" + getEnabled() +
                '}';
    }
}

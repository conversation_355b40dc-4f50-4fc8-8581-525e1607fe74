package com.example.restapi.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Pattern;

@Entity
@DiscriminatorValue("AGENT")
public class AgentUser extends User {

    @NotBlank(message = "Agent ID is required")
    @Size(max = 20, message = "Agent ID must not exceed 20 characters")
    @Pattern(regexp = "^[A-Z0-9]+$", message = "Agent ID must contain only uppercase letters and numbers")
    @Column(name = "agent_id", unique = true, nullable = false, length = 20)
    private String agentId;

    @Size(max = 100, message = "Agency name must not exceed 100 characters")
    @Column(name = "agency_name", length = 100)
    private String agencyName;

    @Size(max = 50, message = "License number must not exceed 50 characters")
    @Column(name = "license_number", length = 50)
    private String licenseNumber;

    @Size(max = 50, message = "Territory must not exceed 50 characters")
    @Column(name = "territory", length = 50)
    private String territory;

    @Column(name = "commission_rate")
    private Double commissionRate;

    // Constructors
    public AgentUser() {
        super();
    }

    public AgentUser(String username, String email, String password, String firstName, String lastName, String agentId) {
        super(username, email, password, firstName, lastName);
        this.agentId = agentId;
    }

    @Override
    public String getUserType() {
        return "AGENT";
    }

    // Getters and Setters
    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    public String getLicenseNumber() {
        return licenseNumber;
    }

    public void setLicenseNumber(String licenseNumber) {
        this.licenseNumber = licenseNumber;
    }

    public String getTerritory() {
        return territory;
    }

    public void setTerritory(String territory) {
        this.territory = territory;
    }

    public Double getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(Double commissionRate) {
        this.commissionRate = commissionRate;
    }

    @Override
    public String toString() {
        return "AgentUser{" +
                "id=" + getId() +
                ", username='" + getUsername() + '\'' +
                ", email='" + getEmail() + '\'' +
                ", firstName='" + getFirstName() + '\'' +
                ", lastName='" + getLastName() + '\'' +
                ", agentId='" + agentId + '\'' +
                ", agencyName='" + agencyName + '\'' +
                ", licenseNumber='" + licenseNumber + '\'' +
                ", territory='" + territory + '\'' +
                ", commissionRate=" + commissionRate +
                ", enabled=" + getEnabled() +
                '}';
    }
}

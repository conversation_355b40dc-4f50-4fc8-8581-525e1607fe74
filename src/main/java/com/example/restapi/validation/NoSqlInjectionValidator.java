package com.example.restapi.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

public class NoSqlInjectionValidator implements ConstraintValidator<NoSqlInjection, String> {

    // Patterns to detect potential SQL injection and NoSQL injection attempts
    private static final Pattern[] DANGEROUS_PATTERNS = {
        Pattern.compile(".*['\";\\\\].*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\$where.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\$ne.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\$gt.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\$lt.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\$regex.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\$or.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\$and.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\$in.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\$nin.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*javascript:.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*<script.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*</script>.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*eval\\s*\\(.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*function\\s*\\(.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\bselect\\b.*\\bfrom\\b.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\binsert\\b.*\\binto\\b.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\bupdate\\b.*\\bset\\b.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\bdelete\\b.*\\bfrom\\b.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\bdrop\\b.*\\btable\\b.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*\\bunion\\b.*\\bselect\\b.*", Pattern.CASE_INSENSITIVE)
    };

    @Override
    public void initialize(NoSqlInjection constraintAnnotation) {
        // No initialization needed
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.trim().isEmpty()) {
            return true; // Let @NotBlank handle empty values
        }

        // Check against dangerous patterns
        for (Pattern pattern : DANGEROUS_PATTERNS) {
            if (pattern.matcher(value).matches()) {
                return false;
            }
        }

        // Additional checks for suspicious character sequences
        if (containsSuspiciousCharacterSequences(value)) {
            return false;
        }

        return true;
    }

    private boolean containsSuspiciousCharacterSequences(String value) {
        // Check for multiple consecutive special characters
        if (value.matches(".*[!@#$%^&*()+={}\\[\\]|\\\\:;\"'<>?,./]{3,}.*")) {
            return true;
        }

        // Check for encoded characters that might be used for injection
        if (value.contains("%27") || value.contains("%22") || value.contains("%3C") || value.contains("%3E")) {
            return true;
        }

        // Check for null bytes
        if (value.contains("\0")) {
            return true;
        }

        return false;
    }
}

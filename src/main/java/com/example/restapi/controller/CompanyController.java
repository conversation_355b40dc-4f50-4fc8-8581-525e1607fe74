package com.example.restapi.controller;

import com.example.restapi.dto.CompanyDto;
import com.example.restapi.dto.UserGroupDto;
import com.example.restapi.entity.Company;
import com.example.restapi.entity.UserGroup;
import com.example.restapi.exception.ResourceNotFoundException;
import com.example.restapi.service.CompanyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/companies")
@Tag(name = "Company Management", description = "APIs for managing companies")
@SecurityRequirement(name = "bearerAuth")
public class CompanyController {

    private static final Logger logger = LoggerFactory.getLogger(CompanyController.class);

    @Autowired
    private CompanyService companyService;

    @Operation(summary = "Create a new company", description = "Creates a new company")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Company created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "409", description = "Company name or registration number already exists")
    })
    @PostMapping
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<CompanyDto> createCompany(@Valid @RequestBody CompanyDto companyDto) {
        logger.info("Creating company with name: {}", companyDto.getName());

        Company company = convertToEntity(companyDto);
        Company savedCompany = companyService.createCompany(company);
        CompanyDto responseDto = convertToDto(savedCompany);

        return new ResponseEntity<>(responseDto, HttpStatus.CREATED);
    }

    @Operation(summary = "Get company by ID", description = "Retrieves a company by its unique identifier")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Company found"),
        @ApiResponse(responseCode = "404", description = "Company not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<CompanyDto> getCompanyById(@Parameter(description = "Company ID") @PathVariable Long id) {
        Company company = companyService.getCompanyById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + id));
        
        return ResponseEntity.ok(convertToDto(company));
    }

    @Operation(summary = "Get all companies", description = "Retrieves a paginated list of all companies")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Companies retrieved successfully")
    })
    @GetMapping
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<Page<CompanyDto>> getAllCompanies(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "name") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "asc") String sortDir,
            @Parameter(description = "Include only active companies") @RequestParam(defaultValue = "true") boolean activeOnly) {

        Sort sort = sortDir.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<Company> companies = activeOnly ? 
            companyService.getAllActiveCompanies(pageable) : 
            companyService.getAllCompanies(pageable);
        
        Page<CompanyDto> companyDtos = companies.map(this::convertToDto);
        return ResponseEntity.ok(companyDtos);
    }

    @Operation(summary = "Search companies", description = "Searches companies by name, description, industry, or registration number")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully")
    })
    @GetMapping("/search")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<Page<CompanyDto>> searchCompanies(
            @Parameter(description = "Search term") @RequestParam String q,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Include only active companies") @RequestParam(defaultValue = "true") boolean activeOnly) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("name").ascending());
        Page<Company> companies = activeOnly ? 
            companyService.searchActiveCompanies(q, pageable) : 
            companyService.searchCompanies(q, pageable);
        
        Page<CompanyDto> companyDtos = companies.map(this::convertToDto);
        return ResponseEntity.ok(companyDtos);
    }

    @Operation(summary = "Update company", description = "Updates an existing company's information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Company updated successfully"),
        @ApiResponse(responseCode = "404", description = "Company not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input data")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<CompanyDto> updateCompany(
            @Parameter(description = "Company ID") @PathVariable Long id,
            @Valid @RequestBody CompanyDto companyDto) {

        Company company = convertToEntity(companyDto);
        Company updatedCompany = companyService.updateCompany(id, company);
        return ResponseEntity.ok(convertToDto(updatedCompany));
    }

    @Operation(summary = "Delete company", description = "Permanently deletes a company")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Company deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Company not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('CORPORATE')")
    public ResponseEntity<Void> deleteCompany(@Parameter(description = "Company ID") @PathVariable Long id) {
        companyService.deleteCompany(id);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Archive company", description = "Archives a company")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Company archived successfully"),
        @ApiResponse(responseCode = "404", description = "Company not found")
    })
    @PatchMapping("/{id}/archive")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Map<String, String>> archiveCompany(@Parameter(description = "Company ID") @PathVariable Long id) {
        companyService.archiveCompany(id);
        return ResponseEntity.ok(Map.of("message", "Company archived successfully"));
    }

    @Operation(summary = "Activate company", description = "Activates a company")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Company activated successfully"),
        @ApiResponse(responseCode = "404", description = "Company not found")
    })
    @PatchMapping("/{id}/activate")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Map<String, String>> activateCompany(@Parameter(description = "Company ID") @PathVariable Long id) {
        companyService.activateCompany(id);
        return ResponseEntity.ok(Map.of("message", "Company activated successfully"));
    }

    @Operation(summary = "Add user group to company", description = "Associates a user group with a company")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User group added to company successfully"),
        @ApiResponse(responseCode = "404", description = "Company or user group not found"),
        @ApiResponse(responseCode = "400", description = "User group already associated with company")
    })
    @PostMapping("/{companyId}/user-groups/{userGroupId}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Map<String, String>> addUserGroupToCompany(
            @Parameter(description = "Company ID") @PathVariable Long companyId,
            @Parameter(description = "User group ID") @PathVariable Long userGroupId) {
        
        companyService.addUserGroupToCompany(companyId, userGroupId);
        return ResponseEntity.ok(Map.of("message", "User group added to company successfully"));
    }

    @Operation(summary = "Remove user group from company", description = "Removes a user group association from a company")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User group removed from company successfully"),
        @ApiResponse(responseCode = "404", description = "Company or user group not found"),
        @ApiResponse(responseCode = "400", description = "User group not associated with company")
    })
    @DeleteMapping("/{companyId}/user-groups/{userGroupId}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Map<String, String>> removeUserGroupFromCompany(
            @Parameter(description = "Company ID") @PathVariable Long companyId,
            @Parameter(description = "User group ID") @PathVariable Long userGroupId) {
        
        companyService.removeUserGroupFromCompany(companyId, userGroupId);
        return ResponseEntity.ok(Map.of("message", "User group removed from company successfully"));
    }

    @Operation(summary = "Get user groups for company", description = "Retrieves all user groups associated with a company")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User groups retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Company not found")
    })
    @GetMapping("/{companyId}/user-groups")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<Page<UserGroupDto>> getUserGroupsForCompany(
            @Parameter(description = "Company ID") @PathVariable Long companyId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("name").ascending());
        Page<UserGroup> userGroups = companyService.getUserGroupsForCompany(companyId, pageable);
        Page<UserGroupDto> userGroupDtos = userGroups.map(this::convertUserGroupToDto);

        return ResponseEntity.ok(userGroupDtos);
    }

    @Operation(summary = "Get companies by industry", description = "Retrieves companies filtered by industry")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Companies retrieved successfully")
    })
    @GetMapping("/industry/{industry}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<Page<CompanyDto>> getCompaniesByIndustry(
            @Parameter(description = "Industry") @PathVariable String industry,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("name").ascending());
        Page<Company> companies = companyService.getActiveCompaniesByIndustry(industry, pageable);
        Page<CompanyDto> companyDtos = companies.map(this::convertToDto);

        return ResponseEntity.ok(companyDtos);
    }

    @Operation(summary = "Get distinct industries", description = "Retrieves all distinct industries")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Industries retrieved successfully")
    })
    @GetMapping("/industries")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<List<String>> getDistinctIndustries() {
        List<String> industries = companyService.getDistinctIndustries();
        return ResponseEntity.ok(industries);
    }

    @Operation(summary = "Get distinct countries", description = "Retrieves all distinct countries")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Countries retrieved successfully")
    })
    @GetMapping("/countries")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<List<String>> getDistinctCountries() {
        List<String> countries = companyService.getDistinctCountries();
        return ResponseEntity.ok(countries);
    }

    // Helper methods
    private Company convertToEntity(CompanyDto dto) {
        Company company = new Company();
        company.setId(dto.getId());
        company.setName(dto.getName());
        company.setDescription(dto.getDescription());
        company.setRegistrationNumber(dto.getRegistrationNumber());
        company.setTaxId(dto.getTaxId());
        company.setStatus(dto.getStatus());
        company.setIndustry(dto.getIndustry());
        company.setCompanySize(dto.getCompanySize());
        company.setEmail(dto.getEmail());
        company.setPhoneNumber(dto.getPhoneNumber());
        company.setWebsite(dto.getWebsite());
        company.setAddressLine1(dto.getAddressLine1());
        company.setAddressLine2(dto.getAddressLine2());
        company.setCity(dto.getCity());
        company.setState(dto.getState());
        company.setPostalCode(dto.getPostalCode());
        company.setCountry(dto.getCountry());
        return company;
    }

    private CompanyDto convertToDto(Company company) {
        CompanyDto dto = new CompanyDto();
        dto.setId(company.getId());
        dto.setName(company.getName());
        dto.setDescription(company.getDescription());
        dto.setRegistrationNumber(company.getRegistrationNumber());
        dto.setTaxId(company.getTaxId());
        dto.setStatus(company.getStatus());
        dto.setIndustry(company.getIndustry());
        dto.setCompanySize(company.getCompanySize());
        dto.setEmail(company.getEmail());
        dto.setPhoneNumber(company.getPhoneNumber());
        dto.setWebsite(company.getWebsite());
        dto.setAddressLine1(company.getAddressLine1());
        dto.setAddressLine2(company.getAddressLine2());
        dto.setCity(company.getCity());
        dto.setState(company.getState());
        dto.setPostalCode(company.getPostalCode());
        dto.setCountry(company.getCountry());
        dto.setUserGroupCount(company.getUserGroupCount());
        dto.setCreatedAt(company.getCreatedAt());
        dto.setUpdatedAt(company.getUpdatedAt());
        return dto;
    }

    private UserGroupDto convertUserGroupToDto(UserGroup userGroup) {
        UserGroupDto dto = new UserGroupDto();
        dto.setId(userGroup.getId());
        dto.setName(userGroup.getName());
        dto.setDescription(userGroup.getDescription());
        dto.setStatus(userGroup.getStatus());
        dto.setGroupType(userGroup.getGroupType());
        dto.setMaxMembers(userGroup.getMaxMembers());
        dto.setCurrentMemberCount(userGroup.getUserCount());
        dto.setCreatedAt(userGroup.getCreatedAt());
        dto.setUpdatedAt(userGroup.getUpdatedAt());
        return dto;
    }
}

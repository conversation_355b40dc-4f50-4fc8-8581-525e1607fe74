package com.example.restapi.controller;

import com.example.restapi.dto.UserDto;
import com.example.restapi.dto.UserGroupDto;
import com.example.restapi.entity.User;
import com.example.restapi.entity.UserGroup;
import com.example.restapi.exception.ResourceNotFoundException;
import com.example.restapi.service.UserGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/user-groups")
@Tag(name = "User Group Management", description = "APIs for managing user groups")
@SecurityRequirement(name = "bearerAuth")
public class UserGroupController {

    private static final Logger logger = LoggerFactory.getLogger(UserGroupController.class);

    @Autowired
    private UserGroupService userGroupService;

    @Operation(summary = "Create a new user group", description = "Creates a new user group")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "User group created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "409", description = "Group name already exists")
    })
    @PostMapping
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<UserGroupDto> createUserGroup(@Valid @RequestBody UserGroupDto userGroupDto) {
        logger.info("Creating user group with name: {}", userGroupDto.getName());

        UserGroup userGroup = convertToEntity(userGroupDto);
        UserGroup savedGroup = userGroupService.createUserGroup(userGroup);
        UserGroupDto responseDto = convertToDto(savedGroup);

        return new ResponseEntity<>(responseDto, HttpStatus.CREATED);
    }

    @Operation(summary = "Get user group by ID", description = "Retrieves a user group by its unique identifier")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User group found"),
        @ApiResponse(responseCode = "404", description = "User group not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<UserGroupDto> getUserGroupById(@Parameter(description = "User group ID") @PathVariable Long id) {
        UserGroup userGroup = userGroupService.getUserGroupById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User group not found with id: " + id));
        
        return ResponseEntity.ok(convertToDto(userGroup));
    }

    @Operation(summary = "Get all user groups", description = "Retrieves a paginated list of all user groups")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User groups retrieved successfully")
    })
    @GetMapping
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<Page<UserGroupDto>> getAllUserGroups(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "name") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "asc") String sortDir,
            @Parameter(description = "Include only active groups") @RequestParam(defaultValue = "true") boolean activeOnly) {

        Sort sort = sortDir.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<UserGroup> groups = activeOnly ? 
            userGroupService.getAllActiveUserGroups(pageable) : 
            userGroupService.getAllUserGroups(pageable);
        
        Page<UserGroupDto> groupDtos = groups.map(this::convertToDto);
        return ResponseEntity.ok(groupDtos);
    }

    @Operation(summary = "Search user groups", description = "Searches user groups by name, description, or type")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully")
    })
    @GetMapping("/search")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<Page<UserGroupDto>> searchUserGroups(
            @Parameter(description = "Search term") @RequestParam String q,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Include only active groups") @RequestParam(defaultValue = "true") boolean activeOnly) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("name").ascending());
        Page<UserGroup> groups = activeOnly ? 
            userGroupService.searchActiveUserGroups(q, pageable) : 
            userGroupService.searchUserGroups(q, pageable);
        
        Page<UserGroupDto> groupDtos = groups.map(this::convertToDto);
        return ResponseEntity.ok(groupDtos);
    }

    @Operation(summary = "Update user group", description = "Updates an existing user group's information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User group updated successfully"),
        @ApiResponse(responseCode = "404", description = "User group not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input data")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<UserGroupDto> updateUserGroup(
            @Parameter(description = "User group ID") @PathVariable Long id,
            @Valid @RequestBody UserGroupDto userGroupDto) {

        UserGroup userGroup = convertToEntity(userGroupDto);
        UserGroup updatedGroup = userGroupService.updateUserGroup(id, userGroup);
        return ResponseEntity.ok(convertToDto(updatedGroup));
    }

    @Operation(summary = "Delete user group", description = "Permanently deletes a user group")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "User group deleted successfully"),
        @ApiResponse(responseCode = "404", description = "User group not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('CORPORATE')")
    public ResponseEntity<Void> deleteUserGroup(@Parameter(description = "User group ID") @PathVariable Long id) {
        userGroupService.deleteUserGroup(id);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Archive user group", description = "Archives a user group")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User group archived successfully"),
        @ApiResponse(responseCode = "404", description = "User group not found")
    })
    @PatchMapping("/{id}/archive")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Map<String, String>> archiveUserGroup(@Parameter(description = "User group ID") @PathVariable Long id) {
        userGroupService.archiveUserGroup(id);
        return ResponseEntity.ok(Map.of("message", "User group archived successfully"));
    }

    @Operation(summary = "Activate user group", description = "Activates a user group")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User group activated successfully"),
        @ApiResponse(responseCode = "404", description = "User group not found")
    })
    @PatchMapping("/{id}/activate")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Map<String, String>> activateUserGroup(@Parameter(description = "User group ID") @PathVariable Long id) {
        userGroupService.activateUserGroup(id);
        return ResponseEntity.ok(Map.of("message", "User group activated successfully"));
    }

    @Operation(summary = "Add user to group", description = "Adds a user to a user group")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User added to group successfully"),
        @ApiResponse(responseCode = "404", description = "User or group not found"),
        @ApiResponse(responseCode = "400", description = "User already in group or group is full")
    })
    @PostMapping("/{groupId}/users/{userId}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Map<String, String>> addUserToGroup(
            @Parameter(description = "User group ID") @PathVariable Long groupId,
            @Parameter(description = "User ID") @PathVariable Long userId) {
        
        userGroupService.addUserToGroup(groupId, userId);
        return ResponseEntity.ok(Map.of("message", "User added to group successfully"));
    }

    @Operation(summary = "Remove user from group", description = "Removes a user from a user group")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User removed from group successfully"),
        @ApiResponse(responseCode = "404", description = "User or group not found"),
        @ApiResponse(responseCode = "400", description = "User not in group")
    })
    @DeleteMapping("/{groupId}/users/{userId}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Map<String, String>> removeUserFromGroup(
            @Parameter(description = "User group ID") @PathVariable Long groupId,
            @Parameter(description = "User ID") @PathVariable Long userId) {
        
        userGroupService.removeUserFromGroup(groupId, userId);
        return ResponseEntity.ok(Map.of("message", "User removed from group successfully"));
    }

    @Operation(summary = "Get users in group", description = "Retrieves all users in a specific group")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Users retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Group not found")
    })
    @GetMapping("/{groupId}/users")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<Page<UserDto>> getUsersInGroup(
            @Parameter(description = "User group ID") @PathVariable Long groupId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("firstName").ascending());
        Page<User> users = userGroupService.getUsersInGroup(groupId, pageable);
        Page<UserDto> userDtos = users.map(this::convertUserToDto);

        return ResponseEntity.ok(userDtos);
    }

    @Operation(summary = "Get groups by type", description = "Retrieves user groups filtered by type")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Groups retrieved successfully")
    })
    @GetMapping("/type/{groupType}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<Page<UserGroupDto>> getGroupsByType(
            @Parameter(description = "Group type") @PathVariable String groupType,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("name").ascending());
        Page<UserGroup> groups = userGroupService.getActiveUserGroupsByType(groupType, pageable);
        Page<UserGroupDto> groupDtos = groups.map(this::convertToDto);

        return ResponseEntity.ok(groupDtos);
    }

    // Helper methods
    private UserGroup convertToEntity(UserGroupDto dto) {
        UserGroup userGroup = new UserGroup();
        userGroup.setId(dto.getId());
        userGroup.setName(dto.getName());
        userGroup.setDescription(dto.getDescription());
        userGroup.setStatus(dto.getStatus());
        userGroup.setGroupType(dto.getGroupType());
        userGroup.setMaxMembers(dto.getMaxMembers());
        return userGroup;
    }

    private UserGroupDto convertToDto(UserGroup userGroup) {
        UserGroupDto dto = new UserGroupDto();
        dto.setId(userGroup.getId());
        dto.setName(userGroup.getName());
        dto.setDescription(userGroup.getDescription());
        dto.setStatus(userGroup.getStatus());
        dto.setGroupType(userGroup.getGroupType());
        dto.setMaxMembers(userGroup.getMaxMembers());
        dto.setCurrentMemberCount(userGroup.getUserCount());
        dto.setCreatedAt(userGroup.getCreatedAt());
        dto.setUpdatedAt(userGroup.getUpdatedAt());
        return dto;
    }

    private UserDto convertUserToDto(User user) {
        UserDto dto = new UserDto();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setEmail(user.getEmail());
        dto.setFirstName(user.getFirstName());
        dto.setLastName(user.getLastName());
        dto.setPhoneNumber(user.getPhoneNumber());
        dto.setUserType(user.getUserType());
        dto.setEnabled(user.getEnabled());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        return dto;
    }
}

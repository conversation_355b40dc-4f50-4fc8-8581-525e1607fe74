package com.example.restapi.controller;

import com.example.restapi.dto.CreateUserRequest;
import com.example.restapi.dto.UserDto;
import com.example.restapi.entity.*;
import com.example.restapi.exception.BadRequestException;
import com.example.restapi.exception.ResourceNotFoundException;
import com.example.restapi.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/users")
@Tag(name = "User Management", description = "APIs for managing users")
@SecurityRequirement(name = "bearerAuth")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    @Operation(summary = "Create a new user", description = "Creates a new user with the specified type (CORPORATE, AGENT, or ASSOCIATE)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "User created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "409", description = "Username or email already exists")
    })
    @PostMapping
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<UserDto> createUser(@Valid @RequestBody CreateUserRequest request) {
        logger.info("Creating user with username: {}", request.getUsername());

        User user = createUserFromRequest(request);
        User savedUser = userService.createUser(user);
        UserDto userDto = convertToDto(savedUser);

        return new ResponseEntity<>(userDto, HttpStatus.CREATED);
    }

    @Operation(summary = "Get user by ID", description = "Retrieves a user by their unique identifier")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User found"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT') or hasRole('ASSOCIATE')")
    public ResponseEntity<UserDto> getUserById(@Parameter(description = "User ID") @PathVariable Long id) {
        User user = userService.getUserById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));
        
        return ResponseEntity.ok(convertToDto(user));
    }

    @Operation(summary = "Get all users", description = "Retrieves a paginated list of all users")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Users retrieved successfully")
    })
    @GetMapping
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Page<UserDto>> getAllUsers(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "id") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "asc") String sortDir,
            @Parameter(description = "Include only active users") @RequestParam(defaultValue = "true") boolean activeOnly) {

        Sort sort = sortDir.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<User> users = activeOnly ? userService.getAllActiveUsers(pageable) : userService.getAllUsers(pageable);
        Page<UserDto> userDtos = users.map(this::convertToDto);

        return ResponseEntity.ok(userDtos);
    }

    @Operation(summary = "Search users", description = "Searches users by username, email, first name, or last name")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully")
    })
    @GetMapping("/search")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Page<UserDto>> searchUsers(
            @Parameter(description = "Search term") @RequestParam String q,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Include only active users") @RequestParam(defaultValue = "true") boolean activeOnly) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("firstName").ascending());
        Page<User> users = activeOnly ? 
            userService.searchActiveUsers(q, pageable) : 
            userService.searchUsers(q, pageable);
        
        Page<UserDto> userDtos = users.map(this::convertToDto);
        return ResponseEntity.ok(userDtos);
    }

    @Operation(summary = "Update user", description = "Updates an existing user's information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User updated successfully"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input data")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<UserDto> updateUser(
            @Parameter(description = "User ID") @PathVariable Long id,
            @Valid @RequestBody UserDto userDto) {

        User existingUser = userService.getUserById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));

        // Update basic fields
        existingUser.setUsername(userDto.getUsername());
        existingUser.setEmail(userDto.getEmail());
        existingUser.setFirstName(userDto.getFirstName());
        existingUser.setLastName(userDto.getLastName());
        existingUser.setPhoneNumber(userDto.getPhoneNumber());

        User updatedUser = userService.updateUser(id, existingUser);
        return ResponseEntity.ok(convertToDto(updatedUser));
    }

    @Operation(summary = "Delete user", description = "Permanently deletes a user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "User deleted successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('CORPORATE')")
    public ResponseEntity<Void> deleteUser(@Parameter(description = "User ID") @PathVariable Long id) {
        userService.deleteUser(id);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Disable user", description = "Disables a user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User disabled successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @PatchMapping("/{id}/disable")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Map<String, String>> disableUser(@Parameter(description = "User ID") @PathVariable Long id) {
        userService.disableUser(id);
        return ResponseEntity.ok(Map.of("message", "User disabled successfully"));
    }

    @Operation(summary = "Enable user", description = "Enables a user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User enabled successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @PatchMapping("/{id}/enable")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Map<String, String>> enableUser(@Parameter(description = "User ID") @PathVariable Long id) {
        userService.enableUser(id);
        return ResponseEntity.ok(Map.of("message", "User enabled successfully"));
    }

    @Operation(summary = "Get users by type", description = "Retrieves users filtered by their type")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Users retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid user type")
    })
    @GetMapping("/type/{userType}")
    @PreAuthorize("hasRole('CORPORATE') or hasRole('AGENT')")
    public ResponseEntity<Page<UserDto>> getUsersByType(
            @Parameter(description = "User type (CORPORATE, AGENT, ASSOCIATE)") @PathVariable String userType,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {

        Class<? extends User> userClass = getUserClassByType(userType);
        Pageable pageable = PageRequest.of(page, size, Sort.by("firstName").ascending());
        Page<User> users = userService.getUsersByTypeAndEnabled(userClass, pageable);
        Page<UserDto> userDtos = users.map(this::convertToDto);

        return ResponseEntity.ok(userDtos);
    }

    // Helper methods
    private User createUserFromRequest(CreateUserRequest request) {
        User user;
        
        switch (request.getUserType().toUpperCase()) {
            case "CORPORATE":
                CorporateUser corporateUser = new CorporateUser(
                    request.getUsername(), request.getEmail(), request.getPassword(),
                    request.getFirstName(), request.getLastName(), request.getCompanyName()
                );
                corporateUser.setDepartment(request.getDepartment());
                corporateUser.setJobTitle(request.getJobTitle());
                corporateUser.setEmployeeId(request.getEmployeeId());
                user = corporateUser;
                break;
                
            case "AGENT":
                AgentUser agentUser = new AgentUser(
                    request.getUsername(), request.getEmail(), request.getPassword(),
                    request.getFirstName(), request.getLastName(), request.getAgentId()
                );
                agentUser.setAgencyName(request.getAgencyName());
                agentUser.setLicenseNumber(request.getLicenseNumber());
                agentUser.setTerritory(request.getTerritory());
                agentUser.setCommissionRate(request.getCommissionRate());
                user = agentUser;
                break;
                
            case "ASSOCIATE":
                AssociateUser associateUser = new AssociateUser(
                    request.getUsername(), request.getEmail(), request.getPassword(),
                    request.getFirstName(), request.getLastName(), request.getAssociateId()
                );
                associateUser.setOrganizationName(request.getOrganizationName());
                associateUser.setCertification(request.getCertification());
                associateUser.setSpecialization(request.getSpecialization());
                associateUser.setYearsOfExperience(request.getYearsOfExperience());
                user = associateUser;
                break;
                
            default:
                throw new BadRequestException("Invalid user type: " + request.getUserType());
        }
        
        user.setPhoneNumber(request.getPhoneNumber());
        return user;
    }

    private UserDto convertToDto(User user) {
        UserDto dto = new UserDto();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setEmail(user.getEmail());
        dto.setFirstName(user.getFirstName());
        dto.setLastName(user.getLastName());
        dto.setPhoneNumber(user.getPhoneNumber());
        dto.setUserType(user.getUserType());
        dto.setEnabled(user.getEnabled());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        return dto;
    }

    private Class<? extends User> getUserClassByType(String userType) {
        switch (userType.toUpperCase()) {
            case "CORPORATE":
                return CorporateUser.class;
            case "AGENT":
                return AgentUser.class;
            case "ASSOCIATE":
                return AssociateUser.class;
            default:
                throw new BadRequestException("Invalid user type: " + userType);
        }
    }
}

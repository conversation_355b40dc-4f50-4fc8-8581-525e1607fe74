package com.example.restapi.controller;

import com.example.restapi.dto.CreateUserRequest;
import com.example.restapi.dto.JwtResponse;
import com.example.restapi.dto.LoginRequest;
import com.example.restapi.dto.UserDto;
import com.example.restapi.entity.*;
import com.example.restapi.exception.BadRequestException;
import com.example.restapi.security.JwtUtils;
import com.example.restapi.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/auth")
@Tag(name = "Authentication", description = "Authentication and user registration APIs")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtils jwtUtils;

    @Operation(summary = "User login", description = "Authenticates a user and returns JWT tokens")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Login successful"),
        @ApiResponse(responseCode = "401", description = "Invalid credentials")
    })
    @PostMapping("/login")
    public ResponseEntity<JwtResponse> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        logger.info("Login attempt for username: {}", loginRequest.getUsername());

        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword()));

        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        String jwt = jwtUtils.generateJwtToken(authentication);
        String refreshToken = jwtUtils.generateRefreshToken(authentication);
        
        User userDetails = (User) authentication.getPrincipal();
        List<String> roles = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        logger.info("User {} logged in successfully", userDetails.getUsername());

        return ResponseEntity.ok(new JwtResponse(jwt, refreshToken, userDetails.getId(),
                userDetails.getUsername(), userDetails.getEmail(), userDetails.getUserType(), roles));
    }

    @Operation(summary = "Register new user", description = "Registers a new user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "User registered successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "409", description = "Username or email already exists")
    })
    @PostMapping("/register")
    public ResponseEntity<UserDto> registerUser(@Valid @RequestBody CreateUserRequest signUpRequest) {
        logger.info("Registration attempt for username: {}", signUpRequest.getUsername());

        if (userService.existsByUsername(signUpRequest.getUsername())) {
            throw new BadRequestException("Username is already taken!");
        }

        if (userService.existsByEmail(signUpRequest.getEmail())) {
            throw new BadRequestException("Email is already in use!");
        }

        // Create user based on type
        User user = createUserFromRequest(signUpRequest);
        User savedUser = userService.createUser(user);
        
        logger.info("User {} registered successfully", savedUser.getUsername());

        UserDto userDto = convertToDto(savedUser);
        return new ResponseEntity<>(userDto, HttpStatus.CREATED);
    }

    @Operation(summary = "Refresh token", description = "Refreshes the JWT access token using a refresh token")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Token refreshed successfully"),
        @ApiResponse(responseCode = "401", description = "Invalid refresh token")
    })
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, String>> refreshToken(@RequestBody Map<String, String> request) {
        String refreshToken = request.get("refreshToken");
        
        if (refreshToken == null || !jwtUtils.validateJwtToken(refreshToken)) {
            throw new BadRequestException("Invalid refresh token");
        }

        String username = jwtUtils.getUserNameFromJwtToken(refreshToken);
        User user = userService.getUserByUsername(username)
                .orElseThrow(() -> new BadRequestException("User not found"));

        String newAccessToken = jwtUtils.generateTokenFromUsername(username, (int) jwtUtils.getExpirationTime());
        
        return ResponseEntity.ok(Map.of(
            "accessToken", newAccessToken,
            "tokenType", "Bearer"
        ));
    }

    @Operation(summary = "Logout", description = "Logs out the current user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Logout successful")
    })
    @PostMapping("/logout")
    public ResponseEntity<Map<String, String>> logoutUser() {
        SecurityContextHolder.clearContext();
        return ResponseEntity.ok(Map.of("message", "Logout successful"));
    }

    // Helper methods
    private User createUserFromRequest(CreateUserRequest request) {
        User user;
        
        switch (request.getUserType().toUpperCase()) {
            case "CORPORATE":
                CorporateUser corporateUser = new CorporateUser(
                    request.getUsername(), request.getEmail(), request.getPassword(),
                    request.getFirstName(), request.getLastName(), request.getCompanyName()
                );
                corporateUser.setDepartment(request.getDepartment());
                corporateUser.setJobTitle(request.getJobTitle());
                corporateUser.setEmployeeId(request.getEmployeeId());
                user = corporateUser;
                break;
                
            case "AGENT":
                AgentUser agentUser = new AgentUser(
                    request.getUsername(), request.getEmail(), request.getPassword(),
                    request.getFirstName(), request.getLastName(), request.getAgentId()
                );
                agentUser.setAgencyName(request.getAgencyName());
                agentUser.setLicenseNumber(request.getLicenseNumber());
                agentUser.setTerritory(request.getTerritory());
                agentUser.setCommissionRate(request.getCommissionRate());
                user = agentUser;
                break;
                
            case "ASSOCIATE":
                AssociateUser associateUser = new AssociateUser(
                    request.getUsername(), request.getEmail(), request.getPassword(),
                    request.getFirstName(), request.getLastName(), request.getAssociateId()
                );
                associateUser.setOrganizationName(request.getOrganizationName());
                associateUser.setCertification(request.getCertification());
                associateUser.setSpecialization(request.getSpecialization());
                associateUser.setYearsOfExperience(request.getYearsOfExperience());
                user = associateUser;
                break;
                
            default:
                throw new BadRequestException("Invalid user type: " + request.getUserType());
        }
        
        user.setPhoneNumber(request.getPhoneNumber());
        return user;
    }

    private UserDto convertToDto(User user) {
        UserDto dto = new UserDto();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setEmail(user.getEmail());
        dto.setFirstName(user.getFirstName());
        dto.setLastName(user.getLastName());
        dto.setPhoneNumber(user.getPhoneNumber());
        dto.setUserType(user.getUserType());
        dto.setEnabled(user.getEnabled());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        return dto;
    }
}

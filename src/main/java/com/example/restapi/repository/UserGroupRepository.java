package com.example.restapi.repository;

import com.example.restapi.entity.UserGroup;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserGroupRepository extends JpaRepository<UserGroup, Long> {

    Optional<UserGroup> findByName(String name);

    Boolean existsByName(String name);

    @Query("SELECT ug FROM UserGroup ug WHERE ug.status = 'ACTIVE'")
    List<UserGroup> findAllActiveGroups();

    @Query("SELECT ug FROM UserGroup ug WHERE ug.status = 'ACTIVE'")
    Page<UserGroup> findAllActiveGroups(Pageable pageable);

    @Query("SELECT ug FROM UserGroup ug WHERE ug.status = :status")
    Page<UserGroup> findByStatus(@Param("status") UserGroup.GroupStatus status, Pageable pageable);

    @Query("SELECT ug FROM UserGroup ug WHERE ug.groupType = :groupType")
    Page<UserGroup> findByGroupType(@Param("groupType") String groupType, Pageable pageable);

    @Query("SELECT ug FROM UserGroup ug WHERE ug.groupType = :groupType AND ug.status = 'ACTIVE'")
    Page<UserGroup> findActiveGroupsByType(@Param("groupType") String groupType, Pageable pageable);

    @Query("SELECT ug FROM UserGroup ug WHERE " +
           "LOWER(ug.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(ug.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(ug.groupType) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<UserGroup> searchGroups(@Param("searchTerm") String searchTerm, Pageable pageable);

    @Query("SELECT ug FROM UserGroup ug WHERE ug.status = 'ACTIVE' AND (" +
           "LOWER(ug.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(ug.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(ug.groupType) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<UserGroup> searchActiveGroups(@Param("searchTerm") String searchTerm, Pageable pageable);

    @Query("SELECT ug FROM UserGroup ug JOIN ug.users u WHERE u.id = :userId")
    List<UserGroup> findByUserId(@Param("userId") Long userId);

    @Query("SELECT ug FROM UserGroup ug JOIN ug.users u WHERE u.id = :userId AND ug.status = 'ACTIVE'")
    Page<UserGroup> findActiveGroupsByUserId(@Param("userId") Long userId, Pageable pageable);

    @Query("SELECT ug FROM UserGroup ug JOIN ug.companies c WHERE c.id = :companyId")
    List<UserGroup> findByCompanyId(@Param("companyId") Long companyId);

    @Query("SELECT ug FROM UserGroup ug JOIN ug.companies c WHERE c.id = :companyId AND ug.status = 'ACTIVE'")
    Page<UserGroup> findActiveGroupsByCompanyId(@Param("companyId") Long companyId, Pageable pageable);

    @Query("SELECT COUNT(ug) FROM UserGroup ug WHERE ug.status = :status")
    Long countByStatus(@Param("status") UserGroup.GroupStatus status);

    @Query("SELECT COUNT(ug) FROM UserGroup ug WHERE ug.groupType = :groupType AND ug.status = 'ACTIVE'")
    Long countActiveGroupsByType(@Param("groupType") String groupType);

    @Query("SELECT ug FROM UserGroup ug WHERE ug.maxMembers IS NOT NULL AND SIZE(ug.users) >= ug.maxMembers")
    List<UserGroup> findFullGroups();

    @Query("SELECT ug FROM UserGroup ug WHERE ug.maxMembers IS NULL OR SIZE(ug.users) < ug.maxMembers")
    List<UserGroup> findAvailableGroups();
}

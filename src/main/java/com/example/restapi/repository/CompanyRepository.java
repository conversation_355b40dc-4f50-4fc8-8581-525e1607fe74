package com.example.restapi.repository;

import com.example.restapi.entity.Company;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CompanyRepository extends JpaRepository<Company, Long> {

    Optional<Company> findByName(String name);

    Optional<Company> findByRegistrationNumber(String registrationNumber);

    Boolean existsByName(String name);

    Boolean existsByRegistrationNumber(String registrationNumber);

    @Query("SELECT c FROM Company c WHERE c.status = 'ACTIVE'")
    List<Company> findAllActiveCompanies();

    @Query("SELECT c FROM Company c WHERE c.status = 'ACTIVE'")
    Page<Company> findAllActiveCompanies(Pageable pageable);

    @Query("SELECT c FROM Company c WHERE c.status = :status")
    Page<Company> findByStatus(@Param("status") Company.CompanyStatus status, Pageable pageable);

    @Query("SELECT c FROM Company c WHERE c.industry = :industry")
    Page<Company> findByIndustry(@Param("industry") String industry, Pageable pageable);

    @Query("SELECT c FROM Company c WHERE c.industry = :industry AND c.status = 'ACTIVE'")
    Page<Company> findActiveCompaniesByIndustry(@Param("industry") String industry, Pageable pageable);

    @Query("SELECT c FROM Company c WHERE c.companySize = :companySize")
    Page<Company> findByCompanySize(@Param("companySize") String companySize, Pageable pageable);

    @Query("SELECT c FROM Company c WHERE c.companySize = :companySize AND c.status = 'ACTIVE'")
    Page<Company> findActiveCompaniesBySize(@Param("companySize") String companySize, Pageable pageable);

    @Query("SELECT c FROM Company c WHERE " +
           "LOWER(c.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.industry) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.registrationNumber) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<Company> searchCompanies(@Param("searchTerm") String searchTerm, Pageable pageable);

    @Query("SELECT c FROM Company c WHERE c.status = 'ACTIVE' AND (" +
           "LOWER(c.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.industry) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.registrationNumber) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Company> searchActiveCompanies(@Param("searchTerm") String searchTerm, Pageable pageable);

    @Query("SELECT c FROM Company c JOIN c.userGroups ug WHERE ug.id = :userGroupId")
    List<Company> findByUserGroupId(@Param("userGroupId") Long userGroupId);

    @Query("SELECT c FROM Company c JOIN c.userGroups ug WHERE ug.id = :userGroupId AND c.status = 'ACTIVE'")
    Page<Company> findActiveCompaniesByUserGroupId(@Param("userGroupId") Long userGroupId, Pageable pageable);

    @Query("SELECT c FROM Company c WHERE c.country = :country")
    Page<Company> findByCountry(@Param("country") String country, Pageable pageable);

    @Query("SELECT c FROM Company c WHERE c.country = :country AND c.status = 'ACTIVE'")
    Page<Company> findActiveCompaniesByCountry(@Param("country") String country, Pageable pageable);

    @Query("SELECT c FROM Company c WHERE c.city = :city")
    Page<Company> findByCity(@Param("city") String city, Pageable pageable);

    @Query("SELECT c FROM Company c WHERE c.city = :city AND c.status = 'ACTIVE'")
    Page<Company> findActiveCompaniesByCity(@Param("city") String city, Pageable pageable);

    @Query("SELECT COUNT(c) FROM Company c WHERE c.status = :status")
    Long countByStatus(@Param("status") Company.CompanyStatus status);

    @Query("SELECT COUNT(c) FROM Company c WHERE c.industry = :industry AND c.status = 'ACTIVE'")
    Long countActiveCompaniesByIndustry(@Param("industry") String industry);

    @Query("SELECT COUNT(c) FROM Company c WHERE c.country = :country AND c.status = 'ACTIVE'")
    Long countActiveCompaniesByCountry(@Param("country") String country);

    @Query("SELECT DISTINCT c.industry FROM Company c WHERE c.industry IS NOT NULL AND c.status = 'ACTIVE' ORDER BY c.industry")
    List<String> findDistinctIndustries();

    @Query("SELECT DISTINCT c.country FROM Company c WHERE c.country IS NOT NULL AND c.status = 'ACTIVE' ORDER BY c.country")
    List<String> findDistinctCountries();

    @Query("SELECT DISTINCT c.companySize FROM Company c WHERE c.companySize IS NOT NULL AND c.status = 'ACTIVE' ORDER BY c.companySize")
    List<String> findDistinctCompanySizes();
}

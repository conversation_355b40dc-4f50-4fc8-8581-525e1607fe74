package com.example.restapi.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class SecurityAuditLogger {

    private static final Logger securityLogger = LoggerFactory.getLogger("SECURITY_AUDIT");
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public void logAuthenticationSuccess(String username, String userType, HttpServletRequest request) {
        setMDCContext(username, request);
        securityLogger.info("AUTHENTICATION_SUCCESS - User: {}, Type: {}, IP: {}, UserAgent: {}", 
            username, userType, getClientIpAddress(request), getUserAgent(request));
        clearMDC();
    }

    public void logAuthenticationFailure(String username, String reason, HttpServletRequest request) {
        setMDCContext(username, request);
        securityLogger.warn("AUTHENTICATION_FAILURE - User: {}, Reason: {}, IP: {}, UserAgent: {}", 
            username, reason, getClientIpAddress(request), getUserAgent(request));
        clearMDC();
    }

    public void logUserCreation(String createdBy, String newUsername, String userType) {
        MDC.put("action", "USER_CREATION");
        MDC.put("actor", createdBy);
        securityLogger.info("USER_CREATED - CreatedBy: {}, NewUser: {}, Type: {}", 
            createdBy, newUsername, userType);
        clearMDC();
    }

    public void logUserUpdate(String updatedBy, String targetUsername, String changes) {
        MDC.put("action", "USER_UPDATE");
        MDC.put("actor", updatedBy);
        securityLogger.info("USER_UPDATED - UpdatedBy: {}, TargetUser: {}, Changes: {}", 
            updatedBy, targetUsername, changes);
        clearMDC();
    }

    public void logUserDeletion(String deletedBy, String targetUsername) {
        MDC.put("action", "USER_DELETION");
        MDC.put("actor", deletedBy);
        securityLogger.warn("USER_DELETED - DeletedBy: {}, TargetUser: {}", 
            deletedBy, targetUsername);
        clearMDC();
    }

    public void logUserStatusChange(String changedBy, String targetUsername, String oldStatus, String newStatus) {
        MDC.put("action", "USER_STATUS_CHANGE");
        MDC.put("actor", changedBy);
        securityLogger.info("USER_STATUS_CHANGED - ChangedBy: {}, TargetUser: {}, OldStatus: {}, NewStatus: {}", 
            changedBy, targetUsername, oldStatus, newStatus);
        clearMDC();
    }

    public void logGroupCreation(String createdBy, String groupName, String groupType) {
        MDC.put("action", "GROUP_CREATION");
        MDC.put("actor", createdBy);
        securityLogger.info("GROUP_CREATED - CreatedBy: {}, GroupName: {}, Type: {}", 
            createdBy, groupName, groupType);
        clearMDC();
    }

    public void logGroupUpdate(String updatedBy, String groupName, String changes) {
        MDC.put("action", "GROUP_UPDATE");
        MDC.put("actor", updatedBy);
        securityLogger.info("GROUP_UPDATED - UpdatedBy: {}, GroupName: {}, Changes: {}", 
            updatedBy, groupName, changes);
        clearMDC();
    }

    public void logGroupDeletion(String deletedBy, String groupName) {
        MDC.put("action", "GROUP_DELETION");
        MDC.put("actor", deletedBy);
        securityLogger.warn("GROUP_DELETED - DeletedBy: {}, GroupName: {}", 
            deletedBy, groupName);
        clearMDC();
    }

    public void logUserGroupAssociation(String performedBy, String username, String groupName, String action) {
        MDC.put("action", "USER_GROUP_ASSOCIATION");
        MDC.put("actor", performedBy);
        securityLogger.info("USER_GROUP_{} - PerformedBy: {}, User: {}, Group: {}", 
            action.toUpperCase(), performedBy, username, groupName);
        clearMDC();
    }

    public void logCompanyCreation(String createdBy, String companyName, String registrationNumber) {
        MDC.put("action", "COMPANY_CREATION");
        MDC.put("actor", createdBy);
        securityLogger.info("COMPANY_CREATED - CreatedBy: {}, CompanyName: {}, RegNumber: {}", 
            createdBy, companyName, registrationNumber);
        clearMDC();
    }

    public void logCompanyUpdate(String updatedBy, String companyName, String changes) {
        MDC.put("action", "COMPANY_UPDATE");
        MDC.put("actor", updatedBy);
        securityLogger.info("COMPANY_UPDATED - UpdatedBy: {}, CompanyName: {}, Changes: {}", 
            updatedBy, companyName, changes);
        clearMDC();
    }

    public void logCompanyDeletion(String deletedBy, String companyName) {
        MDC.put("action", "COMPANY_DELETION");
        MDC.put("actor", deletedBy);
        securityLogger.warn("COMPANY_DELETED - DeletedBy: {}, CompanyName: {}", 
            deletedBy, companyName);
        clearMDC();
    }

    public void logCompanyGroupAssociation(String performedBy, String companyName, String groupName, String action) {
        MDC.put("action", "COMPANY_GROUP_ASSOCIATION");
        MDC.put("actor", performedBy);
        securityLogger.info("COMPANY_GROUP_{} - PerformedBy: {}, Company: {}, Group: {}", 
            action.toUpperCase(), performedBy, companyName, groupName);
        clearMDC();
    }

    public void logAccessDenied(String username, String resource, String action, HttpServletRequest request) {
        setMDCContext(username, request);
        securityLogger.warn("ACCESS_DENIED - User: {}, Resource: {}, Action: {}, IP: {}", 
            username, resource, action, getClientIpAddress(request));
        clearMDC();
    }

    public void logSuspiciousActivity(String username, String activity, String details, HttpServletRequest request) {
        setMDCContext(username, request);
        securityLogger.error("SUSPICIOUS_ACTIVITY - User: {}, Activity: {}, Details: {}, IP: {}, UserAgent: {}", 
            username, activity, details, getClientIpAddress(request), getUserAgent(request));
        clearMDC();
    }

    public void logPasswordChange(String username, boolean successful, HttpServletRequest request) {
        setMDCContext(username, request);
        if (successful) {
            securityLogger.info("PASSWORD_CHANGED - User: {}, IP: {}", username, getClientIpAddress(request));
        } else {
            securityLogger.warn("PASSWORD_CHANGE_FAILED - User: {}, IP: {}", username, getClientIpAddress(request));
        }
        clearMDC();
    }

    public void logDataExport(String username, String dataType, int recordCount, HttpServletRequest request) {
        setMDCContext(username, request);
        securityLogger.info("DATA_EXPORT - User: {}, DataType: {}, RecordCount: {}, IP: {}", 
            username, dataType, recordCount, getClientIpAddress(request));
        clearMDC();
    }

    public void logBulkOperation(String username, String operation, String entityType, int affectedCount) {
        MDC.put("action", "BULK_OPERATION");
        MDC.put("actor", username);
        securityLogger.info("BULK_OPERATION - User: {}, Operation: {}, EntityType: {}, AffectedCount: {}", 
            username, operation, entityType, affectedCount);
        clearMDC();
    }

    private void setMDCContext(String username, HttpServletRequest request) {
        MDC.put("username", username != null ? username : "anonymous");
        MDC.put("ip", getClientIpAddress(request));
        MDC.put("timestamp", LocalDateTime.now().format(formatter));
        MDC.put("sessionId", request.getSession(false) != null ? request.getSession().getId() : "none");
    }

    private void clearMDC() {
        MDC.clear();
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    private String getUserAgent(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        return userAgent != null ? userAgent : "unknown";
    }
}

package com.example.restapi.service;

import com.example.restapi.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface UserService {

    User createUser(User user);

    User updateUser(Long id, User user);

    Optional<User> getUserById(Long id);

    Optional<User> getUserByUsername(String username);

    Optional<User> getUserByEmail(String email);

    Page<User> getAllUsers(Pageable pageable);

    Page<User> getAllActiveUsers(Pageable pageable);

    Page<User> searchUsers(String searchTerm, Pageable pageable);

    Page<User> searchActiveUsers(String searchTerm, Pageable pageable);

    List<User> getUsersByType(Class<? extends User> userType);

    Page<User> getUsersByTypeAndEnabled(Class<? extends User> userType, Pageable pageable);

    Page<User> getUsersByGroupId(Long groupId, Pageable pageable);

    void deleteUser(Long id);

    void softDeleteUser(Long id);

    void enableUser(Long id);

    void disableUser(Long id);

    boolean existsByUsername(String username);

    boolean existsByEmail(String email);

    Long countUsersByType(Class<? extends User> userType);

    void changePassword(Long userId, String oldPassword, String newPassword);

    void resetPassword(Long userId, String newPassword);
}

package com.example.restapi.service.impl;

import com.example.restapi.entity.Company;
import com.example.restapi.entity.UserGroup;
import com.example.restapi.exception.BadRequestException;
import com.example.restapi.exception.ResourceNotFoundException;
import com.example.restapi.repository.CompanyRepository;
import com.example.restapi.repository.UserGroupRepository;
import com.example.restapi.service.CompanyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class CompanyServiceImpl implements CompanyService {

    private static final Logger logger = LoggerFactory.getLogger(CompanyServiceImpl.class);

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private UserGroupRepository userGroupRepository;

    @Override
    public Company createCompany(Company company) {
        logger.info("Creating new company with name: {}", company.getName());

        if (companyRepository.existsByName(company.getName())) {
            throw new BadRequestException("Company name is already taken!");
        }

        if (company.getRegistrationNumber() != null && 
            companyRepository.existsByRegistrationNumber(company.getRegistrationNumber())) {
            throw new BadRequestException("Registration number is already in use!");
        }

        // Set default status if not provided
        if (company.getStatus() == null) {
            company.setStatus(Company.CompanyStatus.ACTIVE);
        }

        Company savedCompany = companyRepository.save(company);
        logger.info("Company created successfully with ID: {}", savedCompany.getId());
        return savedCompany;
    }

    @Override
    public Company updateCompany(Long id, Company company) {
        logger.info("Updating company with ID: {}", id);

        Company existingCompany = companyRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + id));

        // Check if name is being changed and if it's already taken
        if (!existingCompany.getName().equals(company.getName()) && 
            companyRepository.existsByName(company.getName())) {
            throw new BadRequestException("Company name is already taken!");
        }

        // Check if registration number is being changed and if it's already taken
        if (company.getRegistrationNumber() != null && 
            !company.getRegistrationNumber().equals(existingCompany.getRegistrationNumber()) &&
            companyRepository.existsByRegistrationNumber(company.getRegistrationNumber())) {
            throw new BadRequestException("Registration number is already in use!");
        }

        // Update fields
        existingCompany.setName(company.getName());
        existingCompany.setDescription(company.getDescription());
        existingCompany.setRegistrationNumber(company.getRegistrationNumber());
        existingCompany.setTaxId(company.getTaxId());
        existingCompany.setIndustry(company.getIndustry());
        existingCompany.setCompanySize(company.getCompanySize());
        existingCompany.setEmail(company.getEmail());
        existingCompany.setPhoneNumber(company.getPhoneNumber());
        existingCompany.setWebsite(company.getWebsite());
        
        // Update address fields
        existingCompany.setAddressLine1(company.getAddressLine1());
        existingCompany.setAddressLine2(company.getAddressLine2());
        existingCompany.setCity(company.getCity());
        existingCompany.setState(company.getState());
        existingCompany.setPostalCode(company.getPostalCode());
        existingCompany.setCountry(company.getCountry());

        if (company.getStatus() != null) {
            existingCompany.setStatus(company.getStatus());
        }

        Company updatedCompany = companyRepository.save(existingCompany);
        logger.info("Company updated successfully with ID: {}", updatedCompany.getId());
        return updatedCompany;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Company> getCompanyById(Long id) {
        return companyRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Company> getCompanyByName(String name) {
        return companyRepository.findByName(name);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Company> getCompanyByRegistrationNumber(String registrationNumber) {
        return companyRepository.findByRegistrationNumber(registrationNumber);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> getAllCompanies(Pageable pageable) {
        return companyRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> getAllActiveCompanies(Pageable pageable) {
        return companyRepository.findAllActiveCompanies(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> searchCompanies(String searchTerm, Pageable pageable) {
        return companyRepository.searchCompanies(searchTerm, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> searchActiveCompanies(String searchTerm, Pageable pageable) {
        return companyRepository.searchActiveCompanies(searchTerm, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> getCompaniesByStatus(Company.CompanyStatus status, Pageable pageable) {
        return companyRepository.findByStatus(status, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> getCompaniesByIndustry(String industry, Pageable pageable) {
        return companyRepository.findByIndustry(industry, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> getActiveCompaniesByIndustry(String industry, Pageable pageable) {
        return companyRepository.findActiveCompaniesByIndustry(industry, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> getCompaniesBySize(String companySize, Pageable pageable) {
        return companyRepository.findByCompanySize(companySize, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> getActiveCompaniesBySize(String companySize, Pageable pageable) {
        return companyRepository.findActiveCompaniesBySize(companySize, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> getCompaniesByCountry(String country, Pageable pageable) {
        return companyRepository.findByCountry(country, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> getActiveCompaniesByCountry(String country, Pageable pageable) {
        return companyRepository.findActiveCompaniesByCountry(country, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> getCompaniesByCity(String city, Pageable pageable) {
        return companyRepository.findByCity(city, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> getActiveCompaniesByCity(String city, Pageable pageable) {
        return companyRepository.findActiveCompaniesByCity(city, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Company> getCompaniesByUserGroupId(Long userGroupId, Pageable pageable) {
        return companyRepository.findActiveCompaniesByUserGroupId(userGroupId, pageable);
    }

    @Override
    public void deleteCompany(Long id) {
        logger.info("Deleting company with ID: {}", id);

        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + id));

        // Remove all user group associations before deleting
        company.getUserGroups().clear();
        companyRepository.save(company);
        
        companyRepository.delete(company);
        logger.info("Company deleted successfully with ID: {}", id);
    }

    @Override
    public void archiveCompany(Long id) {
        logger.info("Archiving company with ID: {}", id);
        updateCompanyStatus(id, Company.CompanyStatus.ARCHIVED);
    }

    @Override
    public void activateCompany(Long id) {
        logger.info("Activating company with ID: {}", id);
        updateCompanyStatus(id, Company.CompanyStatus.ACTIVE);
    }

    @Override
    public void suspendCompany(Long id) {
        logger.info("Suspending company with ID: {}", id);
        updateCompanyStatus(id, Company.CompanyStatus.SUSPENDED);
    }

    private void updateCompanyStatus(Long id, Company.CompanyStatus status) {
        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + id));

        company.setStatus(status);
        companyRepository.save(company);
        logger.info("Company status updated to {} for ID: {}", status, id);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String name) {
        return companyRepository.existsByName(name);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByRegistrationNumber(String registrationNumber) {
        return companyRepository.existsByRegistrationNumber(registrationNumber);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countCompaniesByStatus(Company.CompanyStatus status) {
        return companyRepository.countByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countActiveCompaniesByIndustry(String industry) {
        return companyRepository.countActiveCompaniesByIndustry(industry);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countActiveCompaniesByCountry(String country) {
        return companyRepository.countActiveCompaniesByCountry(country);
    }

    @Override
    public void addUserGroupToCompany(Long companyId, Long userGroupId) {
        logger.info("Adding user group {} to company {}", userGroupId, companyId);

        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));

        UserGroup userGroup = userGroupRepository.findById(userGroupId)
                .orElseThrow(() -> new ResourceNotFoundException("User group not found with id: " + userGroupId));

        if (!company.isActive()) {
            throw new BadRequestException("Cannot add user group to inactive company");
        }

        if (!userGroup.isActive()) {
            throw new BadRequestException("Cannot add inactive user group to company");
        }

        if (company.hasUserGroup(userGroup)) {
            throw new BadRequestException("User group is already associated with this company");
        }

        company.addUserGroup(userGroup);
        companyRepository.save(company);
        logger.info("User group {} added to company {} successfully", userGroupId, companyId);
    }

    @Override
    public void removeUserGroupFromCompany(Long companyId, Long userGroupId) {
        logger.info("Removing user group {} from company {}", userGroupId, companyId);

        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));

        UserGroup userGroup = userGroupRepository.findById(userGroupId)
                .orElseThrow(() -> new ResourceNotFoundException("User group not found with id: " + userGroupId));

        if (!company.hasUserGroup(userGroup)) {
            throw new BadRequestException("User group is not associated with this company");
        }

        company.removeUserGroup(userGroup);
        companyRepository.save(company);
        logger.info("User group {} removed from company {} successfully", userGroupId, companyId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isUserGroupAssociatedWithCompany(Long companyId, Long userGroupId) {
        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));

        UserGroup userGroup = userGroupRepository.findById(userGroupId)
                .orElseThrow(() -> new ResourceNotFoundException("User group not found with id: " + userGroupId));

        return company.hasUserGroup(userGroup);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserGroup> getUserGroupsForCompany(Long companyId, Pageable pageable) {
        return userGroupRepository.findActiveGroupsByCompanyId(companyId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getDistinctIndustries() {
        return companyRepository.findDistinctIndustries();
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getDistinctCountries() {
        return companyRepository.findDistinctCountries();
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getDistinctCompanySizes() {
        return companyRepository.findDistinctCompanySizes();
    }
}

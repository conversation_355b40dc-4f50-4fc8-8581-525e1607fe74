package com.example.restapi.service.impl;

import com.example.restapi.entity.User;
import com.example.restapi.entity.UserGroup;
import com.example.restapi.exception.BadRequestException;
import com.example.restapi.exception.ResourceNotFoundException;
import com.example.restapi.repository.UserGroupRepository;
import com.example.restapi.repository.UserRepository;
import com.example.restapi.service.UserGroupService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class UserGroupServiceImpl implements UserGroupService {

    private static final Logger logger = LoggerFactory.getLogger(UserGroupServiceImpl.class);

    @Autowired
    private UserGroupRepository userGroupRepository;

    @Autowired
    private UserRepository userRepository;

    @Override
    public UserGroup createUserGroup(UserGroup userGroup) {
        logger.info("Creating new user group with name: {}", userGroup.getName());

        if (userGroupRepository.existsByName(userGroup.getName())) {
            throw new BadRequestException("User group name is already taken!");
        }

        // Set default status if not provided
        if (userGroup.getStatus() == null) {
            userGroup.setStatus(UserGroup.GroupStatus.ACTIVE);
        }

        UserGroup savedGroup = userGroupRepository.save(userGroup);
        logger.info("User group created successfully with ID: {}", savedGroup.getId());
        return savedGroup;
    }

    @Override
    public UserGroup updateUserGroup(Long id, UserGroup userGroup) {
        logger.info("Updating user group with ID: {}", id);

        UserGroup existingGroup = userGroupRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User group not found with id: " + id));

        // Check if name is being changed and if it's already taken
        if (!existingGroup.getName().equals(userGroup.getName()) && 
            userGroupRepository.existsByName(userGroup.getName())) {
            throw new BadRequestException("User group name is already taken!");
        }

        // Update fields
        existingGroup.setName(userGroup.getName());
        existingGroup.setDescription(userGroup.getDescription());
        existingGroup.setGroupType(userGroup.getGroupType());
        existingGroup.setMaxMembers(userGroup.getMaxMembers());
        
        if (userGroup.getStatus() != null) {
            existingGroup.setStatus(userGroup.getStatus());
        }

        UserGroup updatedGroup = userGroupRepository.save(existingGroup);
        logger.info("User group updated successfully with ID: {}", updatedGroup.getId());
        return updatedGroup;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<UserGroup> getUserGroupById(Long id) {
        return userGroupRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<UserGroup> getUserGroupByName(String name) {
        return userGroupRepository.findByName(name);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserGroup> getAllUserGroups(Pageable pageable) {
        return userGroupRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserGroup> getAllActiveUserGroups(Pageable pageable) {
        return userGroupRepository.findAllActiveGroups(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserGroup> searchUserGroups(String searchTerm, Pageable pageable) {
        return userGroupRepository.searchGroups(searchTerm, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserGroup> searchActiveUserGroups(String searchTerm, Pageable pageable) {
        return userGroupRepository.searchActiveGroups(searchTerm, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserGroup> getUserGroupsByStatus(UserGroup.GroupStatus status, Pageable pageable) {
        return userGroupRepository.findByStatus(status, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserGroup> getUserGroupsByType(String groupType, Pageable pageable) {
        return userGroupRepository.findByGroupType(groupType, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserGroup> getActiveUserGroupsByType(String groupType, Pageable pageable) {
        return userGroupRepository.findActiveGroupsByType(groupType, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserGroup> getUserGroupsByUserId(Long userId, Pageable pageable) {
        return userGroupRepository.findActiveGroupsByUserId(userId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserGroup> getUserGroupsByCompanyId(Long companyId, Pageable pageable) {
        return userGroupRepository.findActiveGroupsByCompanyId(companyId, pageable);
    }

    @Override
    public void deleteUserGroup(Long id) {
        logger.info("Deleting user group with ID: {}", id);

        UserGroup userGroup = userGroupRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User group not found with id: " + id));

        // Remove all user associations before deleting
        userGroup.getUsers().clear();
        userGroup.getCompanies().clear();
        userGroupRepository.save(userGroup);
        
        userGroupRepository.delete(userGroup);
        logger.info("User group deleted successfully with ID: {}", id);
    }

    @Override
    public void archiveUserGroup(Long id) {
        logger.info("Archiving user group with ID: {}", id);
        updateUserGroupStatus(id, UserGroup.GroupStatus.ARCHIVED);
    }

    @Override
    public void activateUserGroup(Long id) {
        logger.info("Activating user group with ID: {}", id);
        updateUserGroupStatus(id, UserGroup.GroupStatus.ACTIVE);
    }

    @Override
    public void suspendUserGroup(Long id) {
        logger.info("Suspending user group with ID: {}", id);
        updateUserGroupStatus(id, UserGroup.GroupStatus.SUSPENDED);
    }

    private void updateUserGroupStatus(Long id, UserGroup.GroupStatus status) {
        UserGroup userGroup = userGroupRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User group not found with id: " + id));

        userGroup.setStatus(status);
        userGroupRepository.save(userGroup);
        logger.info("User group status updated to {} for ID: {}", status, id);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String name) {
        return userGroupRepository.existsByName(name);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countUserGroupsByStatus(UserGroup.GroupStatus status) {
        return userGroupRepository.countByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countActiveUserGroupsByType(String groupType) {
        return userGroupRepository.countActiveGroupsByType(groupType);
    }

    @Override
    public void addUserToGroup(Long groupId, Long userId) {
        logger.info("Adding user {} to group {}", userId, groupId);

        UserGroup userGroup = userGroupRepository.findById(groupId)
                .orElseThrow(() -> new ResourceNotFoundException("User group not found with id: " + groupId));

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        if (!user.isEnabled()) {
            throw new BadRequestException("Cannot add disabled user to group");
        }

        if (!userGroup.isActive()) {
            throw new BadRequestException("Cannot add user to inactive group");
        }

        if (userGroup.hasUser(user)) {
            throw new BadRequestException("User is already in this group");
        }

        userGroup.addUser(user);
        userGroupRepository.save(userGroup);
        logger.info("User {} added to group {} successfully", userId, groupId);
    }

    @Override
    public void removeUserFromGroup(Long groupId, Long userId) {
        logger.info("Removing user {} from group {}", userId, groupId);

        UserGroup userGroup = userGroupRepository.findById(groupId)
                .orElseThrow(() -> new ResourceNotFoundException("User group not found with id: " + groupId));

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        if (!userGroup.hasUser(user)) {
            throw new BadRequestException("User is not in this group");
        }

        userGroup.removeUser(user);
        userGroupRepository.save(userGroup);
        logger.info("User {} removed from group {} successfully", userId, groupId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isUserInGroup(Long groupId, Long userId) {
        UserGroup userGroup = userGroupRepository.findById(groupId)
                .orElseThrow(() -> new ResourceNotFoundException("User group not found with id: " + groupId));

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        return userGroup.hasUser(user);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<User> getUsersInGroup(Long groupId, Pageable pageable) {
        return userRepository.findActiveUsersByUserGroupId(groupId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserGroup> getAvailableGroups() {
        return userGroupRepository.findAvailableGroups();
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserGroup> getFullGroups() {
        return userGroupRepository.findFullGroups();
    }
}

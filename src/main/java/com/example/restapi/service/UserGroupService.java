package com.example.restapi.service;

import com.example.restapi.entity.User;
import com.example.restapi.entity.UserGroup;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface UserGroupService {

    UserGroup createUserGroup(UserGroup userGroup);

    UserGroup updateUserGroup(Long id, UserGroup userGroup);

    Optional<UserGroup> getUserGroupById(Long id);

    Optional<UserGroup> getUserGroupByName(String name);

    Page<UserGroup> getAllUserGroups(Pageable pageable);

    Page<UserGroup> getAllActiveUserGroups(Pageable pageable);

    Page<UserGroup> searchUserGroups(String searchTerm, Pageable pageable);

    Page<UserGroup> searchActiveUserGroups(String searchTerm, Pageable pageable);

    Page<UserGroup> getUserGroupsByStatus(UserGroup.GroupStatus status, Pageable pageable);

    Page<UserGroup> getUserGroupsByType(String groupType, Pageable pageable);

    Page<UserGroup> getActiveUserGroupsByType(String groupType, Pageable pageable);

    Page<UserGroup> getUserGroupsByUserId(Long userId, Pageable pageable);

    Page<UserGroup> getUserGroupsByCompanyId(Long companyId, Pageable pageable);

    void deleteUserGroup(Long id);

    void archiveUserGroup(Long id);

    void activateUserGroup(Long id);

    void suspendUserGroup(Long id);

    boolean existsByName(String name);

    Long countUserGroupsByStatus(UserGroup.GroupStatus status);

    Long countActiveUserGroupsByType(String groupType);

    // User management methods
    void addUserToGroup(Long groupId, Long userId);

    void removeUserFromGroup(Long groupId, Long userId);

    boolean isUserInGroup(Long groupId, Long userId);

    Page<User> getUsersInGroup(Long groupId, Pageable pageable);

    List<UserGroup> getAvailableGroups();

    List<UserGroup> getFullGroups();
}

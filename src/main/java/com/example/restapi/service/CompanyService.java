package com.example.restapi.service;

import com.example.restapi.entity.Company;
import com.example.restapi.entity.UserGroup;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface CompanyService {

    Company createCompany(Company company);

    Company updateCompany(Long id, Company company);

    Optional<Company> getCompanyById(Long id);

    Optional<Company> getCompanyByName(String name);

    Optional<Company> getCompanyByRegistrationNumber(String registrationNumber);

    Page<Company> getAllCompanies(Pageable pageable);

    Page<Company> getAllActiveCompanies(Pageable pageable);

    Page<Company> searchCompanies(String searchTerm, Pageable pageable);

    Page<Company> searchActiveCompanies(String searchTerm, Pageable pageable);

    Page<Company> getCompaniesByStatus(Company.CompanyStatus status, Pageable pageable);

    Page<Company> getCompaniesByIndustry(String industry, Pageable pageable);

    Page<Company> getActiveCompaniesByIndustry(String industry, Pageable pageable);

    Page<Company> getCompaniesBySize(String companySize, Pageable pageable);

    Page<Company> getActiveCompaniesBySize(String companySize, Pageable pageable);

    Page<Company> getCompaniesByCountry(String country, Pageable pageable);

    Page<Company> getActiveCompaniesByCountry(String country, Pageable pageable);

    Page<Company> getCompaniesByCity(String city, Pageable pageable);

    Page<Company> getActiveCompaniesByCity(String city, Pageable pageable);

    Page<Company> getCompaniesByUserGroupId(Long userGroupId, Pageable pageable);

    void deleteCompany(Long id);

    void archiveCompany(Long id);

    void activateCompany(Long id);

    void suspendCompany(Long id);

    boolean existsByName(String name);

    boolean existsByRegistrationNumber(String registrationNumber);

    Long countCompaniesByStatus(Company.CompanyStatus status);

    Long countActiveCompaniesByIndustry(String industry);

    Long countActiveCompaniesByCountry(String country);

    // UserGroup association methods
    void addUserGroupToCompany(Long companyId, Long userGroupId);

    void removeUserGroupFromCompany(Long companyId, Long userGroupId);

    boolean isUserGroupAssociatedWithCompany(Long companyId, Long userGroupId);

    Page<UserGroup> getUserGroupsForCompany(Long companyId, Pageable pageable);

    // Utility methods
    List<String> getDistinctIndustries();

    List<String> getDistinctCountries();

    List<String> getDistinctCompanySizes();
}

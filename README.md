# Secure User Management REST API

A comprehensive, enterprise-grade REST API for managing users, user groups, and companies with advanced security features.

## Features

### Core Functionality
- **User Management**: Create, update, delete, and search users with three types:
  - Corporate Users
  - Agent Users  
  - Associate Users
- **User Groups**: Manage user groups with user association capabilities
- **Company Management**: Full CRUD operations for companies with user group associations
- **Advanced Search**: Full-text search across all entities

### Security Features
- **JWT Authentication**: Secure token-based authentication with refresh tokens
- **Role-Based Access Control**: Fine-grained permissions based on user types
- **Input Validation**: Comprehensive validation with SQL injection protection
- **Security Headers**: HSTS, CSP, X-Frame-Options, and more
- **Audit Logging**: Complete audit trail for all operations
- **Password Security**: BCrypt encryption with strong password policies

### Technical Features
- **Spring Boot 3.4.1**: Latest Spring Boot with Java 17
- **Spring Security**: Enterprise-grade security framework
- **Spring Data JPA**: Database abstraction with Hibernate
- **OpenAPI/Swagger**: Interactive API documentation
- **Comprehensive Testing**: Unit and integration tests
- **Database Support**: H2 (development) and MySQL (production)

## Quick Start

### Prerequisites
- Java 17 or higher
- Maven 3.6 or higher
- MySQL 8.0+ (for production)

### Running the Application

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd generated-rest-api
   ```

2. **Run with Maven**
   ```bash
   mvn spring-boot:run
   ```

3. **Access the application**
   - API Base URL: `http://localhost:8080`
   - Swagger UI: `http://localhost:8080/swagger-ui.html`
   - H2 Console: `http://localhost:8080/h2-console`

### Database Configuration

#### Development (H2)
The application uses H2 in-memory database by default for development.

#### Production (MySQL)
Update `application.properties`:
```properties
spring.datasource.url=**********************************
spring.datasource.username=your_username
spring.datasource.password=your_password
spring.jpa.hibernate.ddl-auto=validate
```

## API Documentation

### Authentication Endpoints

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "John",
  "lastName": "Doe",
  "userType": "CORPORATE",
  "companyName": "Acme Corp"
}
```

#### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "johndoe",
  "password": "SecurePass123!"
}
```

Response:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "type": "Bearer",
  "id": 1,
  "username": "johndoe",
  "email": "<EMAIL>",
  "userType": "CORPORATE",
  "roles": ["ROLE_CORPORATE"]
}
```

### User Management Endpoints

#### Get All Users
```http
GET /api/users?page=0&size=20&activeOnly=true
Authorization: Bearer <token>
```

#### Create User
```http
POST /api/users
Authorization: Bearer <token>
Content-Type: application/json

{
  "username": "agentsmith",
  "email": "<EMAIL>",
  "password": "AgentPass123!",
  "firstName": "Agent",
  "lastName": "Smith",
  "userType": "AGENT",
  "agentId": "AG001"
}
```

#### Search Users
```http
GET /api/users/search?q=john&activeOnly=true
Authorization: Bearer <token>
```

### User Group Endpoints

#### Create User Group
```http
POST /api/user-groups
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Sales Team",
  "description": "Sales department user group",
  "groupType": "DEPARTMENT",
  "maxMembers": 50
}
```

#### Add User to Group
```http
POST /api/user-groups/{groupId}/users/{userId}
Authorization: Bearer <token>
```

### Company Endpoints

#### Create Company
```http
POST /api/companies
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Tech Solutions Inc",
  "description": "Technology consulting company",
  "registrationNumber": "REG123456",
  "industry": "Technology",
  "email": "<EMAIL>",
  "country": "United States"
}
```

## Security

### Authentication
All endpoints except `/api/auth/**` require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Authorization
- **CORPORATE** users: Full access to all operations
- **AGENT** users: Can manage users and groups, limited company access
- **ASSOCIATE** users: Read-only access to most resources

### Input Validation
- SQL injection protection
- XSS prevention
- Strong password requirements
- Email format validation
- Custom validation annotations

## Testing

### Run Tests
```bash
mvn test
```

### Test Coverage
- Unit tests for services and repositories
- Integration tests for all controllers
- Security testing for authentication and authorization
- Validation testing for input sanitization

## Configuration

### Security Settings
```properties
# JWT Configuration
app.jwt.secret=your-secret-key
app.jwt.expiration=86400000
app.jwt.refresh-expiration=*********

# Security Headers
security.headers.frame-options=DENY
security.headers.content-type-options=nosniff
```

### Logging
```properties
# Application Logging
logging.level.com.example.restapi=INFO

# Security Audit Logging
logging.level.SECURITY_AUDIT=INFO
```

## Monitoring

### Health Check
```http
GET /actuator/health
```

### Metrics
```http
GET /actuator/metrics
Authorization: Bearer <token>
```

## Production Deployment

### Environment Variables
```bash
export SPRING_PROFILES_ACTIVE=production
export DB_URL=**********************************
export DB_USERNAME=your_username
export DB_PASSWORD=your_password
export JWT_SECRET=your-production-secret-key
```

### Docker Deployment
```dockerfile
FROM openjdk:17-jdk-slim
COPY target/generated-rest-api-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: `/swagger-ui.html`
- Issues: GitHub Issues page

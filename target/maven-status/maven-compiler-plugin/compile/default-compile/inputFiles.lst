/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/GeneratedRestApiApplication.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/config/JpaConfig.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/config/OpenApiConfig.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/config/SecurityConfig.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/controller/AuthController.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/controller/CompanyController.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/controller/UserController.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/controller/UserGroupController.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/dto/CompanyDto.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/dto/CreateUserRequest.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/dto/JwtResponse.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/dto/LoginRequest.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/dto/UserDto.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/dto/UserGroupDto.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/entity/AgentUser.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/entity/AssociateUser.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/entity/Company.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/entity/CorporateUser.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/entity/User.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/entity/UserGroup.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/exception/BadRequestException.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/exception/GlobalExceptionHandler.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/exception/ResourceNotFoundException.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/repository/CompanyRepository.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/repository/UserGroupRepository.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/repository/UserRepository.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/security/AuthEntryPointJwt.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/security/AuthTokenFilter.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/security/JwtUtils.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/security/SecurityAuditLogger.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/service/CompanyService.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/service/UserGroupService.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/service/UserService.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/service/impl/CompanyServiceImpl.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/service/impl/UserDetailsServiceImpl.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/service/impl/UserGroupServiceImpl.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/service/impl/UserServiceImpl.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/validation/NoSqlInjection.java
/Users/<USER>/Development/generated-rest-api/src/main/java/com/example/restapi/validation/NoSqlInjectionValidator.java

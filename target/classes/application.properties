# Application Configuration
spring.application.name=generated-rest-api
server.port=8080

# Database Configuration (H2 for development, MySQL for production)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# Security Configuration
app.jwt.secret=mySecretKey123456789012345678901234567890
app.jwt.expiration=86400000
app.jwt.refresh-expiration=604800000

# Actuator Configuration (Secure endpoints)
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
management.endpoints.web.base-path=/actuator

# Logging Configuration
logging.level.com.example.restapi=INFO
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG

# OpenAPI Documentation
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
